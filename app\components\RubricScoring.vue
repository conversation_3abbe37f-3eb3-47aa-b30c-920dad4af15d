<template>
  <div class="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
    <!-- Crite<PERSON> Header -->
    <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <h4 class="font-semibold text-gray-900 dark:text-white">
            {{ criterion.name }}
          </h4>
          <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">
            {{ criterion.description }}
          </p>
        </div>
        <div class="ml-4 text-right">
          <div class="text-lg font-bold text-blue-600 dark:text-blue-400">
            {{ currentScore }}/{{ criterion.maxPoints }}
          </div>
          <div class="text-xs text-gray-500">
            {{ Math.round((currentScore / criterion.maxPoints) * 100) }}%
          </div>
        </div>
      </div>
    </div>

    <!-- Score Selection Table -->
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-100 dark:bg-gray-700">
          <tr>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-16">
              Select
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-24">
              Points
            </th>
            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Performance Level & Description
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          <tr
            v-for="option in pointOptions"
            :key="`${option.level.pointRange}-${option.value}`"
            class="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
            :class="{
              'bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500': currentScore === option.value,
              'hover:bg-blue-25 dark:hover:bg-blue-900/10': currentScore !== option.value,
              'border-t-2 border-gray-300 dark:border-gray-600': option.isHighestInRange
            }"
            @click="selectScore(option.value)"
          >
            <td class="px-4 py-3">
              <input
                type="radio"
                :value="option.value"
                :checked="currentScore === option.value"
                @change="selectScore(option.value)"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 cursor-pointer"
              />
            </td>
            <td class="px-4 py-3">
              <div class="flex flex-col">
                <span class="font-semibold text-gray-900 dark:text-gray-100 text-sm">
                  {{ option.value }} points
                </span>
                <span v-if="option.isHighestInRange" class="text-xs text-gray-500 dark:text-gray-400">
                  ({{ option.level.pointRange }} range)
                </span>
              </div>
            </td>
            <td class="px-4 py-3">
              <span
                v-if="option.isHighestInRange"
                class="text-gray-700 dark:text-gray-300 text-sm"
              >
                {{ option.level.description }}
              </span>
              <span
                v-else
                class="text-gray-500 dark:text-gray-400 text-xs italic"
              >
                {{ option.level.description.split(':')[0] }} ({{ option.value }} pts)
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PointOption, RubricCriterion } from '~/types/grading'
import { getPointsInRange } from '~/utils/grading'

interface Props {
  criterion: RubricCriterion
  currentScore: number
}

interface Emits {
  updateScore: [criterionId: string, score: number]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Generate point options for each level
const pointOptions = computed(() => {
  const options: PointOption[] = []

  // Sort levels by points descending for display
  const sortedLevels = [...props.criterion.levels].sort((a, b) => b.points - a.points)

  for (const level of sortedLevels) {
    const pointsInRange = getPointsInRange(level.pointRange)

    for (let i = 0; i < pointsInRange.length; i++) {
      const pointValue = pointsInRange[i]
      options.push({
        value: pointValue,
        level,
        isHighestInRange: i === 0,
        isLowestInRange: i === pointsInRange.length - 1
      })
    }
  }

  return options
})

// Select score function
const selectScore = (score: number) => {
  emit('updateScore', props.criterion.id, score)
}
</script>
