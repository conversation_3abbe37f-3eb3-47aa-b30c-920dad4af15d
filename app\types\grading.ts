export interface RubricLevel {
  pointRange: string
  points: number
  description: string
}

export interface RubricCriterion {
  id: string
  name: string
  description: string
  maxPoints: number
  levels: RubricLevel[]
}

export interface CriterionScore {
  criterionId: string
  score: number
}

export interface StudentGrade {
  analystId: number
  gradedAt: Date
  criteria: CriterionScore[]
  totalScore: number
  gradedBy: string
}

export interface GradingSession {
  analystId: number
  criteria: CriterionScore[]
  isDraft: boolean
}

// Rubric configuration
export const RUBRIC_CRITERIA: RubricCriterion[] = [
  {
    id: 'task1',
    name: 'Task 1: Cost Analysis (Addition)',
    description:
      'Evaluation of cost function derivation, calculations, and understanding of addition operations',
    maxPoints: 10,
    levels: [
      {
        pointRange: '10-9',
        points: 10,
        description: 'Excellent: All calculations correct, clear work shown, perfect understanding',
      },
      {
        pointRange: '8-7',
        points: 8,
        description: 'Good: Mostly correct with minor computational errors',
      },
      {
        pointRange: '6-5',
        points: 6,
        description: 'Satisfactory: Correct approach but some calculation mistakes',
      },
      {
        pointRange: '4-3',
        points: 4,
        description: 'Needs Improvement: Partial understanding, significant errors',
      },
      { pointRange: '2-0', points: 2, description: 'Incomplete: Missing or completely incorrect' },
    ],
  },
  {
    id: 'task2',
    name: 'Task 2: Profit Analysis (Subtraction)',
    description:
      'Evaluation of profit function derivation, calculations, and understanding of subtraction operations',
    maxPoints: 10,
    levels: [
      {
        pointRange: '10-9',
        points: 10,
        description: 'Excellent: All calculations correct, clear work shown, perfect understanding',
      },
      {
        pointRange: '8-7',
        points: 8,
        description: 'Good: Mostly correct with minor computational errors',
      },
      {
        pointRange: '6-5',
        points: 6,
        description: 'Satisfactory: Correct approach but some calculation mistakes',
      },
      {
        pointRange: '4-3',
        points: 4,
        description: 'Needs Improvement: Partial understanding, significant errors',
      },
      { pointRange: '2-0', points: 2, description: 'Incomplete: Missing or completely incorrect' },
    ],
  },
  {
    id: 'task3',
    name: 'Task 3: Unit Revenue Analysis (Multiplication)',
    description:
      'Evaluation of revenue calculations, price-quantity relationships, and multiplication operations',
    maxPoints: 10,
    levels: [
      {
        pointRange: '10-9',
        points: 10,
        description: 'Excellent: All calculations correct, clear work shown, perfect understanding',
      },
      {
        pointRange: '8-7',
        points: 8,
        description: 'Good: Mostly correct with minor computational errors',
      },
      {
        pointRange: '6-5',
        points: 6,
        description: 'Satisfactory: Correct approach but some calculation mistakes',
      },
      {
        pointRange: '4-3',
        points: 4,
        description: 'Needs Improvement: Partial understanding, significant errors',
      },
      { pointRange: '2-0', points: 2, description: 'Incomplete: Missing or completely incorrect' },
    ],
  },
  {
    id: 'task4',
    name: 'Task 4: Efficiency Analysis (Division)',
    description:
      'Evaluation of average profit calculations, division operations, and limit analysis',
    maxPoints: 10,
    levels: [
      {
        pointRange: '10-9',
        points: 10,
        description: 'Excellent: All calculations correct, clear work shown, perfect understanding',
      },
      {
        pointRange: '8-7',
        points: 8,
        description: 'Good: Mostly correct with minor computational errors',
      },
      {
        pointRange: '6-5',
        points: 6,
        description: 'Satisfactory: Correct approach but some calculation mistakes',
      },
      {
        pointRange: '4-3',
        points: 4,
        description: 'Needs Improvement: Partial understanding, significant errors',
      },
      { pointRange: '2-0', points: 2, description: 'Incomplete: Missing or completely incorrect' },
    ],
  },
  {
    id: 'task5',
    name: 'Task 5: Net Bonus Analysis (Composition)',
    description:
      'Evaluation of function composition, composite calculations, and advanced function operations',
    maxPoints: 10,
    levels: [
      {
        pointRange: '10-9',
        points: 10,
        description: 'Excellent: All calculations correct, clear work shown, perfect understanding',
      },
      {
        pointRange: '8-7',
        points: 8,
        description: 'Good: Mostly correct with minor computational errors',
      },
      {
        pointRange: '6-5',
        points: 6,
        description: 'Satisfactory: Correct approach but some calculation mistakes',
      },
      {
        pointRange: '4-3',
        points: 4,
        description: 'Needs Improvement: Partial understanding, significant errors',
      },
      { pointRange: '2-0', points: 2, description: 'Incomplete: Missing or completely incorrect' },
    ],
  },
  {
    id: 'presentation',
    name: 'Overall Presentation & Work Quality',
    description:
      'Evaluation of work organization, clarity, mathematical notation, and professional presentation',
    maxPoints: 10,
    levels: [
      {
        pointRange: '10-9',
        points: 10,
        description: 'Excellent: Exceptionally well-organized, clear notation, professional format',
      },
      {
        pointRange: '8-7',
        points: 8,
        description: 'Good: Well-organized with minor presentation issues',
      },
      {
        pointRange: '6-5',
        points: 6,
        description: 'Satisfactory: Adequate organization, some unclear work',
      },
      {
        pointRange: '4-3',
        points: 4,
        description: 'Needs Improvement: Poor organization, difficult to follow',
      },
      {
        pointRange: '2-0',
        points: 2,
        description: 'Incomplete: Illegible, extremely disorganized, or missing',
      },
    ],
  },
]

export const TOTAL_POINTS = 60

// Interface for expanded point options in UI
export interface PointOption {
  value: number
  level: RubricLevel
  isHighestInRange: boolean
  isLowestInRange: boolean
}
