// Utility functions for point range handling

export const parsePointRange = (pointRange: string): { min: number; max: number } => {
  const parts = pointRange.split('-').map((p) => parseInt(p.trim(), 10))
  if (
    parts.length === 2 &&
    parts[0] !== undefined &&
    parts[1] !== undefined &&
    !Number.isNaN(parts[0]) &&
    !Number.isNaN(parts[1])
  ) {
    return { min: Math.min(parts[0], parts[1]), max: Math.max(parts[0], parts[1]) }
  }
  // Fallback for single values or invalid ranges
  const singleValue = parseInt(pointRange, 10)
  if (!Number.isNaN(singleValue)) {
    return { min: singleValue, max: singleValue }
  }
  throw new Error(`Invalid point range: ${pointRange}`)
}

export const getPointsInRange = (pointRange: string): number[] => {
  const { min, max } = parsePointRange(pointRange)
  const points: number[] = []
  for (let i = max; i >= min; i--) {
    points.push(i)
  }
  return points
}

export const isValidPointForRange = (point: number, pointRange: string): boolean => {
  try {
    const { min, max } = parsePointRange(pointRange)
    return point >= min && point <= max
  } catch {
    return false
  }
}
